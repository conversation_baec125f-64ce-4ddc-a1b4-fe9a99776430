import { Inject } from 'typescript-ioc';
import { LLMRepository } from '../repository/LLMRepository';
import { QueryUtils } from '@/screens/data-management/views/query-editor/helpers/QueryUtils';
import { DIException } from '@core/common/domain';

export abstract class LLMService {
  abstract generateChart(messages: string): Promise<string>;

  abstract generateSQL(messages: string): Promise<string>;
}

export class LLMServiceImpl implements LLMService {
  @Inject
  private llmRepository!: LLMRepository;

  generateChart(messages: string): Promise<string> {
    return this.llmRepository.generateChart(messages);
  }

  async generateSQL(messages: string): Promise<string> {
    const response = await this.llmRepository.generateSQL(messages);

    if (!QueryUtils.existSQLBlock(response)) {
      throw new DIException(response);
    }

    return response;
  }
}

export class MockLLMService extends LLMService {
  generateChart(_: string): Promise<string> {
    return Promise.resolve(
      `
I'll create a line chart showing profit over time using order dates.

\`\`\`
{
  "chart_type": "line",
  "query_setting": {
    "filters": [],
    "sorts": [],
    "options": {},
    "sql_views": [],
    "parameters": {},
    "x_axis": {
      "name": "Order Date",
      "function": {
        "field": {
          "class_name": "table_field",
          "db_name": "sample",
          "tbl_name": "sales",
          "field_name": "Order_Date",
          "field_type": "datetime"
        },
        "class_name": "group"
      },
      "is_horizontal_view": false,
      "is_collapse": false,
      "is_calc_group_total": true,
      "is_calc_min_max": false,
      "is_dynamic_function": false
    },
    "y_axis": [
      {
        "name": "Total Profit",
        "function": {
          "field": {
            "class_name": "table_field",
            "db_name": "sample",
            "tbl_name": "sales",
            "field_name": "Total_Profit",
            "field_type": "double"
          },
          "class_name": "sum"
        },
        "is_horizontal_view": false,
        "is_collapse": false,
        "is_calc_group_total": true,
        "is_calc_min_max": false,
        "is_dynamic_function": false
      }
    ],
    "legend": null,
    "class_name": "series_chart_setting"
  },
  "filter_requests": [],
  "from": -1,
  "size": -1,
  "dashboard_id": 64,
  "parameters": {}
}
\`\`\`
      `
    );
  }

  async generateSQL(_: string): Promise<string> {
    const response = await Promise.resolve(
      `
I need more information to generate the SQL query using sumWithOverflow. Please provide:

* Which table you want to query (e.g., "sale" for sales data or "org" for organization data)
* What specific column you want to sum with overflow protection (e.g., "Units_Sold", "Number_of_employees", "Total_Revenue")
* Any filters or conditions you'd like to apply (e.g., by country, date range, industry)
* Whether you want to group the results by any column (e.g., by region, country, item type)

For example, are you looking to sum units sold by region, or total profit by country, or something else?
      `
    );

    if (!QueryUtils.existSQLBlock(response)) {
      throw new DIException(response);
    }

    return response;
  }
}
