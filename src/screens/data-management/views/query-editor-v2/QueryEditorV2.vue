<template>
  <Split :gutterSize="24" id="query-editor-v2" @onDragEnd="resizeChart">
    <SplitArea :size="panelSize[0]" :minSize="0">
      <DatabaseTreeView
        ref="databaseTree"
        :mode="DatabaseTreeViewMode.QueryMode"
        :loading="isDatabaseLoading"
        :schemas="databaseSchemas"
        id="db-tree-section"
        show-columns
        @toggleDatabase="onToggleDatabase"
        @clickTable="handleClickTable"
        @clickField="handleClickField"
        @reload="handleReloadDatabases"
      ></DatabaseTreeView>
    </SplitArea>
  </Split>
</template>

<script lang="ts" src="./QueryEditorV2.ctrl.ts" />
<style scoped lang="scss">
#query-editor-v2 {
  .split-horizontal {
    display: flex;
  }
}

#db-tree-section {
  flex: 1;
  margin-right: unset;
}
</style>
