// @ts-ignore
import { Split, SplitArea } from 'vue-split-panel';
import { Component, Mixins, Ref } from 'vue-property-decorator';
import AbstractSchemaComponent, { FindSchemaResponse } from '@/screens/data-management/views/AbstractSchemaComponent';
import SplitPanelMixin from '@/shared/components/layout-wrapper/SplitPanelMixin';
import DatabaseTreeViewCtrl, { DatabaseTreeViewMode } from '@/screens/data-management/components/database-tree-view/DatabaseTreeView';
import { Field, TableSchema } from '@core/common/domain';
import { ConnectionModule } from '@/screens/organization-settings/stores/ConnectionStore';
import { SchemaReloadMode } from '@/store/modules/data-builder/DatabaseSchemaStore';

@Component({
  computed: {
    DatabaseTreeViewMode() {
      return DatabaseTreeViewMode;
    }
  },
  components: {
    Split,
    SplitArea
  }
})
export default class QueryEditorV2 extends Mixins(AbstractSchemaComponent, SplitPanelMixin) {
  @Ref()
  private readonly databaseTree?: DatabaseTreeViewCtrl;

  async mounted() {
    await this.init();
  }

  protected async init() {
    await ConnectionModule.init();
    await this.initDatabaseInfos();
  }

  private async initDatabaseInfos() {
    await this.initShortDatabaseInfos();
    await this.initSelectedTable();
  }

  private async initSelectedTable(): Promise<void> {
    const database: string = this.$route.query?.database?.toString() || '';
    const table: string = this.$route.query?.table?.toString() || '';
    const foundSchema: FindSchemaResponse = await this.findSchema(database, table);
    if (!foundSchema) {
      return Promise.resolve();
    }
    if (foundSchema.database) {
      this.databaseTree?.selectDatabase(foundSchema.database!);
    }
    if (foundSchema.table) {
      this.databaseTree?.selectTable(foundSchema.database!, foundSchema.table);
    }
  }

  private async initShortDatabaseInfos() {
    if (this.reloadShortDatabaseInfos) {
      await this.reloadShortDatabaseInfos(SchemaReloadMode.OnlyShortDatabaseInfo);
    }
  }

  protected get panelSize() {
    return this.getPanelSizeHorizontal();
  }

  protected resizeChart() {
    //TODO: Implement it
  }

  protected onToggleDatabase(dbName: string, isShowing: boolean) {
    //TODO: Implement it
  }

  protected handleClickTable(table: TableSchema) {
    //TODO: Implement it
  }

  protected handleClickField(field: Field) {
    //TODO: Implement it
  }

  protected handleReloadDatabases() {
    //TODO: Implement it
  }
}
