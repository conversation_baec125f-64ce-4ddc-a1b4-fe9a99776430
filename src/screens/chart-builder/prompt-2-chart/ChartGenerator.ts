import { PopupUtils, StringUtils } from '@/utils';
import { ChartType, ConfigType, FunctionTreeNode } from '@/shared';
import { _ConfigBuilderStore } from '@/screens/chart-builder/config-builder/ConfigBuilderStore';
import { Column, DatabaseInfo, DIException, TableSchema } from '@core/common/domain';
import { _BuilderTableSchemaStore } from '@/store/modules/data-builder/BuilderTableSchemaStore';
import { Di } from '@core/common/modules';
import { ChartPromptRequest } from '@/screens/dashboard-detail/intefaces/chatbot/prompt-builder/promt-chart-builder/ChartPromptFactory';
import { ChartBuilderFunction } from '@/screens/dashboard-detail/intefaces/chatbot/functions/ChartBuilderFunction';
import EventBus from '@/screens/dashboard-detail/components/chatbot/helpers/EventBus';
import { ChartBuilderEvent } from '@/screens/chart-builder/config-builder/config-panel/ConfigDraggable';
import { Log } from '@core/utils';

class ChartTypePicker {
  /**
   * Retrieves the current chart type.
   *
   * @returns {ChartType} The current chart type.
   * @throws {DIException} If the current chart type is not supported.
   */
  static get(): ChartType {
    const { type } = _ConfigBuilderStore.itemSelected;

    if (!ChartTypePicker.canGenerateChart(type)) {
      throw new DIException(`${type} is not supported!`);
    }

    return type as ChartType;
  }

  private static canGenerateChart(type: string): boolean {
    return Di.get(ChartBuilderFunction).canExecute(type as ChartType); ///Just mock, will change later
  }
}

export interface ShortInfoColumn {
  name: string;
  type: string;
  description: string;
}

export interface ShortInfoTableSchema {
  database: string;
  table: string;
  columns: ShortInfoColumn[];
}

export interface ShortInfoDatabaseSchema {
  database: string;
  displayName: string;
  tables: ShortInfoTableSchema[];
}

export class TableSchemaPicker {
  /**
   * Retrieves the ShortInfoTableSchema for building a chart.
   *
   * @throws {DIException} If no database and table is selected, or if no table is selected.
   * @returns {ShortInfoTableSchema} The normalized ShortInfoTableSchema.
   */
  static get(): ShortInfoTableSchema {
    this.ensureNotIsEmptyDatabase();
    this.ensureNotIsEmptyTable();
    return this.normalizeTable(_BuilderTableSchemaStore.tableSchemas.find(schema => schema.isExpanded)!.data!);
  }

  static ensureNotIsEmptyDatabase(): void {
    if (this.isEmptyDatabase()) {
      throw new DIException('Please select database and table to build chart');
    }
  }

  static ensureNotIsEmptyTable(): void {
    if (this.isEmptyTable()) {
      throw new DIException('Please select table to build chart');
    }
  }

  static isEmptyDatabase(): boolean {
    return StringUtils.isEmpty(_BuilderTableSchemaStore.selectedDbName.trim()) || !_BuilderTableSchemaStore.databaseSchema;
  }

  static isEmptyTable(): boolean {
    const holdTableSchemaOpening = _BuilderTableSchemaStore.tableSchemas.find(schema => schema.isExpanded);
    return !holdTableSchemaOpening;
  }

  static normalizeTable(tableSchema: TableSchema): ShortInfoTableSchema {
    return {
      database: tableSchema.dbName,
      table: tableSchema.name,
      columns: tableSchema.columns.map(TableSchemaPicker.normalizeColumn)
    };
  }

  static normalizeColumn(column: Column): ShortInfoColumn {
    return {
      name: column.name,
      type: column.className,
      description: column.description ?? ''
    };
  }

  static normalizeDatabase(database: DatabaseInfo): ShortInfoDatabaseSchema {
    return {
      database: database.name,
      displayName: database.displayName,
      tables: database.tables.map(TableSchemaPicker.normalizeTable)
    };
  }
}

export class ChartGenerator {
  async process(prompt: string) {
    try {
      EventBus.$emit(ChartBuilderEvent.analyzingPrompt);
      const response: Map<ConfigType, FunctionTreeNode[]> = await Di.get(ChartBuilderFunction).execute(this.buildChartPromptPayload(prompt));
      EventBus.$emit(ChartBuilderEvent.analyzePromptCompleted);
      response.forEach((nodes, type) => {
        EventBus.$emit(ChartBuilderEvent.addConfig, type, nodes);
      });
    } catch (ex) {
      Log.error('ChartGenerator::process', ex);
      PopupUtils.showError(DIException.fromObject(ex).getPrettyMessage());
    } finally {
      EventBus.$emit(ChartBuilderEvent.analyzePromptCompleted);
    }
  }

  static isValid(prompt: string): boolean {
    return StringUtils.isNotEmpty(prompt.trim());
  }

  private buildChartPromptPayload(prompt: string): ChartPromptRequest {
    return {
      prompt: prompt.trim(),
      chartType: ChartTypePicker.get(),
      tableSchema: TableSchemaPicker.get()
    };
  }
}
