<template>
  <div class="di-msg-bubble-component di-msg-bubble-component__text di-msg-bubble-component--error" :style="{ marginLeft: 'unset' }">
    <IconWarning />
    <span>
      {{ mainData.text }}
      <a href="#" @click.prevent="handleTryAgain">Try again</a>
    </span>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import IconWarning from '@/screens/dashboard-detail/components/chatbot/components/MessageBubble/IconWarning.vue';
import DiButton from '@/shared/components/common/DiButton.vue';
import EventBus from '@/screens/dashboard-detail/components/chatbot/helpers/EventBus';
import { AIChatModalEvents } from '@/screens/dashboard-detail/components/ai-builder-modal/AIChatModalEvents';

@Component({
  components: { DiButton, IconWarning }
})
export default class ErrorMessage extends Vue {
  @Prop() mainData!: ChatMessageData;

  protected handleTryAgain(event: MouseEvent) {
    event.stopPropagation();
    EventBus.$emit(AIChatModalEvents.tryAgain);
  }
}
</script>

<style lang="scss" scoped>
.di-msg-bubble-component--error {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: center;

  a {
    color: var(--accent);
    font-weight: bold;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
