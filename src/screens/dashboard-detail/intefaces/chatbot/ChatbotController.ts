import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { Di } from '@core/common/modules';
import { StringUtils } from '@/utils';
import { DIException, TableSchema } from '@core/common/domain';
import { ChatbotService } from '@core/chat-bot/service/ChatbotService';
import { OpenAiMessage } from '@core/chat-bot/domain/OpenAiMessage';
import { MessageType } from '@/screens/dashboard-detail/intefaces/chatbot/MessageType';
import { ApiKeyInfo, APIKeyService } from '@core/organization';
import { Log } from '@core/utils';
import { OpenAiModels } from '@/screens/dashboard-detail/intefaces/chatbot/OpenAiModels';
import { LLMService } from '@core/chat-bot/service/LLMService';
import { QueryUtils } from '@/screens/data-management/views/query-editor/helpers/QueryUtils';

export abstract class ChatbotController {
  abstract model: string;

  abstract initiated: boolean;

  abstract init(model: OpenAiModels): Promise<void>;

  abstract createSecretKey(key: string): Promise<void>;

  abstract removeSecretKey(): Promise<void>;

  abstract completion(messages: ChatMessageData[]): Promise<ChatMessageData>;
}

export class OpenAiController extends ChatbotController {
  static OPEN_API_KEY = 'open_ai_key';
  static ONE_YEAR_LATER_DURATION = 365 * 24 * 60 * 60 * 1000;
  private apiKey: string | null = null;

  model: OpenAiModels = OpenAiModels.Gpt4Turbo;
  initiated = false;

  async init(model: OpenAiModels): Promise<void> {
    this.initiated = false;
    this.model = model;
    this.apiKey = await this.loadApiKey();
    this.initiated = true;
  }

  async loadApiKey() {
    const apiKeyService = Di.get(APIKeyService);
    const apiKeyInfo: ApiKeyInfo | undefined = (
      await apiKeyService.list({
        keyword: OpenAiController.OPEN_API_KEY
      })
    ).data.find(info => info.displayName === OpenAiController.OPEN_API_KEY);
    Log.debug('loadApiKey::', apiKeyInfo);
    if (StringUtils.isNotEmpty(apiKeyInfo?.apiKey)) {
      return apiKeyInfo!.apiKey;
    } else {
      throw new DIException('No user was found.', 500);
    }
  }

  async createSecretKey(key: string): Promise<void> {
    const apiKeyService = Di.get(APIKeyService);

    const keyResponse = await apiKeyService.create({
      displayName: OpenAiController.OPEN_API_KEY,
      permissions: [],
      expiredTimeMs: OpenAiController.ONE_YEAR_LATER_DURATION,
      apiKey: key
    });

    this.apiKey = keyResponse.apiKeyInfo.apiKey;
  }

  async removeSecretKey() {
    try {
      this.ensureApiKey();
      const apiKeyService = Di.get(APIKeyService);
      await apiKeyService.delete(this.apiKey!);
      this.apiKey = null;
    } catch (error) {
      Log.error(error);
    }
  }

  async completion(messages: ChatMessageData[]): Promise<ChatMessageData> {
    this.ensureApiKey();
    const completionResponse: OpenAiMessage = await Di.get(ChatbotService).completions(this.toOpenAIMessages(messages), {
      model: this.model,
      key: this.apiKey!
    });
    return this.buildBotMessage(completionResponse);
  }

  private toOpenAIMessages(messages: ChatMessageData[]): OpenAiMessage[] {
    return messages.map(message => {
      return {
        role: message.role,
        content: message.text
      };
    });
  }

  private buildBotMessage(res: OpenAiMessage): ChatMessageData {
    return {
      role: res.role,
      type: MessageType.text,
      text: res.content
    };
  }

  private ensureApiKey() {
    if (StringUtils.isNotEmpty(this.apiKey)) {
      return;
    }

    throw new DIException('No user was found.', 500);
  }
}

export type ChartGenerationMetadata = {
  table: TableSchema[];
};

export type QueryGenerationMetadata = {
  table: TableSchema[];
};

export abstract class LLMController {
  abstract generateChart(messages: ChatMessageData[], options: ChartGenerationMetadata): Promise<string>;

  abstract generateSQL(messages: ChatMessageData[], options: QueryGenerationMetadata): Promise<string>;
}

export class RocketBiAiController extends LLMController {
  generateChart(messages: ChatMessageData[], options: ChartGenerationMetadata): Promise<string> {
    const message = this.buildMessage(messages, options);
    return Di.get(LLMService).generateChart(message);
  }

  async generateSQL(messages: ChatMessageData[], options: QueryGenerationMetadata): Promise<string> {
    const message = this.buildMessage(messages, options);
    const response = await Di.get(LLMService).generateSQL(message);
    return QueryUtils.removeLastSemicolon(QueryUtils.extractSQL(response));
  }

  private buildMessage(messages: ChatMessageData[], options: ChartGenerationMetadata) {
    if (messages.length === 0) {
      throw new DIException('No message');
    }

    const lastMessage: ChatMessageData = messages[messages.length - 1];

    const contextPrompt = this.buildChartContextPrompt(options);

    return `${lastMessage.text}. ${contextPrompt}`.trim();
  }

  private buildChartContextPrompt(options: ChartGenerationMetadata) {
    if (options.table.length === 0) {
      return '';
    }
    const tableSchemas = {
      tables: options.table.map(table => {
        return {
          name: table.name,
          db_name: table.dbName,
          columns: table.columns.map(column => {
            return {
              class_name: column.name,
              type: column.className
            };
          })
        };
      })
    };

    return JSON.stringify(tableSchemas);
  }

  private buildChartTypePrompt() {
    return `Pick a chart from prompt to display and return it in response with key chart_type.`;
  }
}
