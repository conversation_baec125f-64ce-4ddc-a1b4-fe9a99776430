import EventBus from '@/screens/dashboard-detail/components/chatbot/helpers/EventBus';
import { Di } from '@core/common/modules';
import { Log } from '@core/utils';
import { ListUtils, PopupUtils, StringUtils } from '@/utils';
import { DatabaseInfo, DIException, TableSchema } from '@core/common/domain';
import { PromptEvents, QueryEditorEvents } from '@/shared/enums/PromptEvents';
import {
  ChatbotController,
  LLMController,
  QueryGenerationMetadata,
  RocketBiAiController
} from '@/screens/dashboard-detail/intefaces/chatbot/ChatbotController';
import { ClickhouseSQLFunction } from '@/screens/dashboard-detail/intefaces/chatbot/functions/ClickhouseSQLFunction';
import { ShortInfoDatabaseSchema, TableSchemaPicker } from '@/screens/chart-builder/prompt-2-chart/ChartGenerator';
import { QueryUtils } from '@/screens/data-management/views/query-editor/helpers/QueryUtils';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { MessageType } from '@/screens/dashboard-detail/intefaces/chatbot/MessageType';
import { OpenAiMessageRole } from '@core/chat-bot/domain/OpenAiMessageRole';

export class QueryGenerator {
  private llm: LLMController = new RocketBiAiController();

  async process(prompt: string, databases: DatabaseInfo[]): Promise<string> {
    try {
      this.ensureDatabases(databases);
      const sqlQuery = await this.completionSQLQuery(prompt, databases);
      this.emitAppendTextEvent(sqlQuery);
      return sqlQuery;
    } catch (ex) {
      Log.error('QueryGenerator::process', ex);
      throw ex;
    } finally {
      EventBus.$emit(PromptEvents.analyzePromptCompleted);
    }
  }

  private selectDatabase(database: DatabaseInfo): ShortInfoDatabaseSchema {
    EventBus.$emit(PromptEvents.analyzingPrompt);
    return TableSchemaPicker.normalizeDatabase(database);
  }

  private async completionSQLQuery(prompt: string, databases: DatabaseInfo[]): Promise<string> {
    const messages = this.buildMessages(prompt);
    const metaData = this.buildMetaData(databases);
    return this.llm.generateSQL(messages, metaData);
  }

  private buildMessages(prompt: string): ChatMessageData[] {
    return [
      {
        role: OpenAiMessageRole.user,
        type: MessageType.text,
        text: prompt
      }
    ];
  }

  private buildMetaData(databases: DatabaseInfo[]): QueryGenerationMetadata {
    return {
      table: databases.reduce((acc, database) => {
        acc.push(...database.tables);
        return acc;
      }, [] as TableSchema[])
    };
  }

  private emitAppendTextEvent(sqlQuery: string) {
    EventBus.$emit(QueryEditorEvents.appendText, `\n${sqlQuery}`);
  }

  private handleError(ex: any) {
    Log.error('QueryGenerator::process', ex);
    const message = DIException.fromObject(ex).getPrettyMessage();
    PopupUtils.showError(message);
  }

  private ensureDatabases(databases: DatabaseInfo[]) {
    if (ListUtils.isEmpty(databases)) {
      throw new DIException('Database is required!');
    }
  }

  static shouldProcessQuery(query: string): boolean {
    const existAICommand = QueryUtils.hasAICommand(query);
    const emptyStringBelowCommand = StringUtils.isEmpty(QueryUtils.getTextAfterAICommand(query));
    return existAICommand && emptyStringBelowCommand;
  }
}
